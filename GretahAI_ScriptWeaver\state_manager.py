"""
State Manager for GretahAI ScriptWeaver

This module provides a centralized state management class for the ScriptWeaver application.
It encapsulates all session state variables and provides helper methods for state mutations.

The StateManager follows a singleton pattern within the Streamlit session state:
1. A single instance is created and stored in st.session_state["state"]
2. All components access this shared instance via StateManager.get(st)
3. State mutations are performed directly on this instance

Key features:
- Organizes state by logical categories (metadata, counters, artifacts)
- Provides type hints for better IDE support and code safety
- Centralizes all state in one place to avoid scattered session_state access
- Includes helper methods for common state operations

Usage:
    # Initialize in main app
    StateManager().init_in_session(st)

    # Access in any function
    state = StateManager.get(st)

    # Mutate state directly
    state.current_step_index += 1
    state.test_data = {"username": "test_user"}
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional
from datetime import datetime
from enum import Enum


class StateStage(Enum):
    """
    Authoritative stage enumeration for GretahAI ScriptWeaver workflow.

    This enum serves as the single source of truth for stage determination,
    replacing the fragile distributed boolean flag interrogation system.
    """
    STAGE1_UPLOAD = "stage1_upload"  # CSV Upload
    STAGE2_WEBSITE = "stage2_website"  # Website Configuration
    STAGE3_CONVERT = "stage3_convert"  # Test Case Analysis and Conversion
    STAGE4_DETECT = "stage4_detect"  # UI Element Detection and Step Selection
    STAGE5_DATA = "stage5_data"  # Manual Data Entry
    STAGE6_GENERATE = "stage6_generate"  # Test Script Generation
    STAGE7_EXECUTE = "stage7_execute"  # Test Script Execution
    STAGE8_OPTIMIZE = "stage8_optimize"  # Script Consolidation and Optimization
    STAGE9_BROWSE = "stage9_browse"  # Script Browser and Comparison

    def __str__(self):
        return self.value

    def get_stage_number(self) -> int:
        """Get the numeric stage number (1-9)."""
        stage_map = {
            StateStage.STAGE1_UPLOAD: 1,
            StateStage.STAGE2_WEBSITE: 2,
            StateStage.STAGE3_CONVERT: 3,
            StateStage.STAGE4_DETECT: 4,
            StateStage.STAGE5_DATA: 5,
            StateStage.STAGE6_GENERATE: 6,
            StateStage.STAGE7_EXECUTE: 7,
            StateStage.STAGE8_OPTIMIZE: 8,
            StateStage.STAGE9_BROWSE: 9
        }
        return stage_map.get(self, 1)

    def get_display_name(self) -> str:
        """Get the human-readable stage name."""
        stage_names = {
            StateStage.STAGE1_UPLOAD: "Stage 1: CSV Upload",
            StateStage.STAGE2_WEBSITE: "Stage 2: Website Configuration",
            StateStage.STAGE3_CONVERT: "Stage 3: Test Case Analysis and Conversion",
            StateStage.STAGE4_DETECT: "Stage 4: UI Element Detection and Step Selection",
            StateStage.STAGE5_DATA: "Stage 5: Manual Data Entry",
            StateStage.STAGE6_GENERATE: "Stage 6: Test Script Generation",
            StateStage.STAGE7_EXECUTE: "Stage 7: Test Script Execution",
            StateStage.STAGE8_OPTIMIZE: "Stage 8: Script Consolidation and Optimization",
            StateStage.STAGE9_BROWSE: "Stage 9: Script Browser and Comparison"
        }
        return stage_names.get(self, "Unknown Stage")

    @classmethod
    def from_number(cls, stage_number: int) -> 'StateStage':
        """Create StateStage from numeric stage number (1-9)."""
        stage_map = {
            1: cls.STAGE1_UPLOAD,
            2: cls.STAGE2_WEBSITE,
            3: cls.STAGE3_CONVERT,
            4: cls.STAGE4_DETECT,
            5: cls.STAGE5_DATA,
            6: cls.STAGE6_GENERATE,
            7: cls.STAGE7_EXECUTE,
            8: cls.STAGE8_OPTIMIZE,
            9: cls.STAGE9_BROWSE
        }
        if stage_number not in stage_map:
            raise ValueError(f"Invalid stage number: {stage_number}. Must be 1-9.")
        return stage_map[stage_number]

@dataclass
class StateManager:
    """
    Centralized state manager for the ScriptWeaver application.

    This class stores all application state in a structured way, organized by logical categories.
    It uses Python's dataclass for clean definition and type hints for better IDE support.

    The state is organized into these categories:
    1. Core test-run metadata - Basic information about the test case and selected steps
    2. Step-progress counters - Track progress through multi-step test cases
    3. Per-step artifacts - Data generated for each step (scripts, analysis, etc.)
    4. Browser and element detection - UI elements and browser instances
    5. Flags - Boolean indicators for application flow control
    6. Usage tracking - Metrics for API usage and performance

    All state mutations should be performed directly on the StateManager instance
    retrieved via StateManager.get(st).
    """

    # ───── Centralized Stage Management ─────
    current_stage: StateStage = StateStage.STAGE1_UPLOAD  # Authoritative current stage (single source of truth)

    # ───── Core test-run metadata ─────
    uploaded_excel: Optional[str] = None  # Path to the uploaded Excel file
    uploaded_file: Optional[str] = None  # Alias for uploaded_excel (for backward compatibility)
    last_file_content_hash: Optional[int] = None  # Hash of the last processed file content
    test_cases: Optional[List[Dict[str, Any]]] = None  # All test cases from Excel
    selected_test_case: Optional[Dict[str, Any]] = None  # Currently selected test case
    original_test_case: Optional[Dict[str, Any]] = None  # Original unmodified test case
    google_api_key: Optional[str] = None  # Google AI API key
    selected_step: Optional[Dict[str, Any]] = None  # Currently selected test step
    selected_step_table_entry: Optional[Dict[str, Any]] = None  # Step in automation-ready format
    step_elements: List[Dict[str, Any]] = field(default_factory=list)  # UI elements for current step
    website_url: Optional[str] = None  # Target website URL

    # ───── Step-progress counters ─────
    current_step_index: int = 0  # Index of current step in step_table_json
    total_steps: int = 0  # Total number of steps in the test case
    all_steps_done: bool = False  # Flag indicating all steps are processed
    completed_steps: List[str] = field(default_factory=list)  # List of completed step numbers
    step_context: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Context from previous steps

    # ───── Per-step artifacts ─────
    step_table_markdown: str = ""  # Markdown representation of step table
    step_table_json: List[Dict[str, Any]] = field(default_factory=list)  # JSON representation of step table
    step_table_analysis: Optional[Dict[str, Any]] = None  # Analysis of step table
    ui_elements: List[Dict[str, Any]] = field(default_factory=list)  # All UI elements
    element_matches: Dict[str, Any] = field(default_factory=dict)  # Matched elements for steps
    step_matches: Dict[str, Any] = field(default_factory=dict)  # Alias for element_matches
    test_data: Dict[str, Any] = field(default_factory=dict)  # Test data for current step
    manual_test_data: Dict[str, Any] = field(default_factory=dict)  # Manually entered test data
    test_data_skipped: bool = False  # Flag indicating test data was skipped
    llm_step_analysis: Dict[str, Any] = field(default_factory=dict)  # LLM analysis of current step
    test_data_analysis: Dict[str, Any] = field(default_factory=dict)  # Analysis of test data requirements
    generated_script_path: Optional[str] = None  # Path to generated test script
    last_script_content: str = ""  # Content of last generated script
    last_script_file: str = ""  # Path to last generated script file
    test_results: Optional[Dict[str, Any]] = None  # Results of test execution
    script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Results from script validation
    validation_feedback_history: List[Dict[str, Any]] = field(default_factory=list)  # Historical validation feedback for learning
    script_regeneration_count: int = field(default=0)  # Track regeneration attempts for feedback loop analysis
    regen_attempts: int = 0  # Track current regeneration attempts for limiting infinite loops

    # ───── Stage 8 optimization artifacts ─────
    optimized_script_path: Optional[str] = None  # Path to optimized script from Stage 8
    optimized_script_content: str = ""  # Content of optimized script
    optimization_in_progress: bool = False  # Flag indicating optimization is in progress
    optimization_complete: bool = False  # Flag indicating optimization is complete
    optimization_start_time: Optional[datetime] = None  # Timestamp when optimization started
    optimization_chunks: List[Dict[str, Any]] = field(default_factory=list)  # Chunks for large script optimization
    optimized_script_test_results: Optional[Dict[str, Any]] = None  # Test results for optimized script
    combined_script_path: Optional[str] = None  # Path to combined script file

    # ───── Stage 8 validation and comment enhancement ─────
    optimized_script_validation_results: Dict[str, Any] = field(default_factory=dict)  # Validation results for optimized script
    optimized_script_validation_done: bool = False  # Flag indicating optimized script validation is complete
    user_optimization_comment: str = ""  # User's raw comment/feedback for script improvement
    ai_enhanced_comment: str = ""  # AI-enhanced version of user's comment
    comment_enhancement_done: bool = False  # Flag indicating AI comment enhancement is complete
    use_enhanced_comment: bool = False  # Flag indicating user chose to use AI-enhanced comment
    regeneration_custom_instructions: Optional[str] = None  # Custom instructions for script regeneration

    # ───── Script continuity tracking ─────
    combined_script_content: Optional[str] = None  # Combined script content for all steps
    script_imports: List[str] = field(default_factory=list)  # List of import statements
    script_fixtures: List[str] = field(default_factory=list)  # List of fixture definitions
    script_variables: Dict[str, str] = field(default_factory=dict)  # Shared variables between steps
    script_functions: Dict[str, str] = field(default_factory=dict)  # Helper functions defined in scripts
    browser_initialized: bool = False  # Flag indicating browser has been initialized
    previous_scripts: Dict[str, str] = field(default_factory=dict)  # Dictionary of previous scripts by step number

    # ───── Script history tracking (Stage 9) ─────
    script_history: List[Dict[str, Any]] = field(default_factory=list)  # Complete history of all generated scripts
    script_metadata: Dict[str, Dict[str, Any]] = field(default_factory=dict)  # Metadata for each script version
    _script_storage: Any = field(default=None, init=False)  # Persistent storage instance

    # ───── Browser and element detection ─────
    test_browser: Any = None  # Browser instance for testing
    detected_elements: List[Dict[str, Any]] = field(default_factory=list)  # All detected UI elements
    qa_relevant_elements: List[Dict[str, Any]] = field(default_factory=list)  # Filtered QA-relevant elements

    # ───── Flags (use one per concept only) ─────
    conversion_done: bool = False  # Flag indicating test case conversion is complete
    step_ready_for_script: bool = False  # Flag indicating step is ready for script generation
    script_just_generated: bool = False  # Flag indicating script was just generated
    use_ai_matching: bool = True  # Flag indicating whether to use AI for element matching
    script_validation_done: bool = False  # Flag indicating script validation is complete
    script_validation_passed: bool = False  # Flag indicating script validation passed

    # ───── Error handling and workflow control ─────
    execution_error_occurred: bool = False  # Flag indicating script execution failed
    execution_error_acknowledged: bool = False  # Flag indicating user acknowledged the error
    execution_error_details: Dict[str, Any] = field(default_factory=dict)  # Error details for display

    # ───── Google AI Studio usage tracking ─────
    google_request_timestamps: List[datetime] = field(default_factory=list)  # Timestamps of API requests
    google_token_usage: List[Any] = field(default_factory=list)  # Token usage per request

    # ───── Helper methods ─────
    def init_in_session(self, st):
        """
        Initialize the state manager in the Streamlit session state.

        This method stores the StateManager instance in st.session_state["state"]
        if it doesn't already exist. This ensures we have a single source of truth
        for application state.

        Args:
            st: The Streamlit module instance
        """
        if "state" not in st.session_state:
            st.session_state["state"] = self  # persist
            # Initialize persistent storage
            self._init_script_storage()
        else:
            # Check if existing state needs upgrades
            existing_state = st.session_state["state"]
            needs_upgrade = (
                not hasattr(existing_state, 'set_execution_error') or
                not hasattr(existing_state, 'current_stage') or
                not hasattr(existing_state, '_script_storage')
            )
            if needs_upgrade:
                # Update existing state with new fields and methods
                self._upgrade_existing_state(existing_state)
                # Initialize persistent storage if not present
                if not hasattr(existing_state, '_script_storage') or existing_state._script_storage is None:
                    existing_state._init_script_storage()

    def _upgrade_existing_state(self, existing_state):
        """
        Upgrade an existing StateManager instance with new capabilities.

        Args:
            existing_state: The existing StateManager instance to upgrade
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Add centralized stage management if it doesn't exist
        if not hasattr(existing_state, 'current_stage'):
            # Initialize current_stage based on existing state
            if hasattr(existing_state, 'update_stage_based_on_completion'):
                # Use the existing method to determine the correct stage
                existing_state.current_stage = StateStage.STAGE1_UPLOAD  # Default
                existing_state.update_stage_based_on_completion()
                logger.info(f"Added current_stage field to existing state: {existing_state.current_stage.get_display_name()}")
            else:
                # Fallback to Stage 1
                existing_state.current_stage = StateStage.STAGE1_UPLOAD
                logger.info("Added current_stage field to existing state: Stage 1 (fallback)")

        # Add new error handling fields if they don't exist
        if not hasattr(existing_state, 'execution_error_occurred'):
            existing_state.execution_error_occurred = False
            logger.info("Added execution_error_occurred field to existing state")

        if not hasattr(existing_state, 'execution_error_acknowledged'):
            existing_state.execution_error_acknowledged = False
            logger.info("Added execution_error_acknowledged field to existing state")

        if not hasattr(existing_state, 'execution_error_details'):
            existing_state.execution_error_details = {}
            logger.info("Added execution_error_details field to existing state")

        # Add new methods to existing state
        existing_state.set_execution_error = self.set_execution_error.__get__(existing_state, StateManager)
        existing_state.acknowledge_execution_error = self.acknowledge_execution_error.__get__(existing_state, StateManager)
        existing_state.clear_execution_error = self.clear_execution_error.__get__(existing_state, StateManager)

        logger.info("Successfully upgraded existing StateManager with new capabilities")

    @staticmethod
    def get(st) -> "StateManager":
        """
        Get the state manager from the Streamlit session state.

        This static method retrieves the StateManager instance from the
        Streamlit session state. It should be used by all components that
        need to access or modify application state.

        Args:
            st: The Streamlit module instance

        Returns:
            StateManager: The singleton StateManager instance
        """
        return st.session_state["state"]

    def update_step_progress(self, current_step_index=None, total_steps=None, all_steps_done=None,
                           step_ready_for_script=None, script_just_generated=None):
        """
        Update step progress counters with validation and logging.

        This method provides a centralized way to update step progress counters,
        ensuring that all updates are properly validated and logged.

        Args:
            current_step_index: New value for current_step_index
            total_steps: New value for total_steps
            all_steps_done: New value for all_steps_done
            step_ready_for_script: New value for step_ready_for_script
            script_just_generated: New value for script_just_generated

        Returns:
            bool: True if any state was updated, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        updated = False

        if current_step_index is not None and current_step_index != self.current_step_index:
            # Validate the new step index
            if total_steps is not None:
                max_index = total_steps - 1
            else:
                max_index = self.total_steps - 1 if self.total_steps > 0 else 0

            if current_step_index < 0 or (max_index > 0 and current_step_index > max_index):
                logger.warning(f"Invalid step index: {current_step_index}, valid range is 0-{max_index}")
                # Clamp to valid range
                current_step_index = max(0, min(current_step_index, max_index))

            if current_step_index != self.current_step_index:
                logger.info(f"State change: current_step_index: {self.current_step_index} -> {current_step_index}")
                self.current_step_index = current_step_index
                updated = True

        if total_steps is not None:
            if total_steps != self.total_steps:
                logger.info(f"State change: total_steps: {self.total_steps} -> {total_steps}")
                self.total_steps = total_steps
                updated = True

        if all_steps_done is not None:
            if all_steps_done != self.all_steps_done:
                logger.info(f"State change: all_steps_done: {self.all_steps_done} -> {all_steps_done}")
                self.all_steps_done = all_steps_done
                updated = True

        if step_ready_for_script is not None:
            if step_ready_for_script != self.step_ready_for_script:
                logger.info(f"State change: step_ready_for_script: {self.step_ready_for_script} -> {step_ready_for_script}")
                self.step_ready_for_script = step_ready_for_script
                updated = True

        if script_just_generated is not None:
            if script_just_generated != self.script_just_generated:
                logger.info(f"State change: script_just_generated: {self.script_just_generated} -> {script_just_generated}")
                self.script_just_generated = script_just_generated
                updated = True

        return updated

    def reset_step_state(self, confirm=False, reason=""):
        """
        Reset step-specific state variables.

        This method provides a centralized way to reset step-specific state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)

        Returns:
            bool: True if state was reset, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not confirm:
            logger.warning(f"Attempted to reset step state without confirmation: {reason}")
            return False

        logger.info(f"Resetting step state: {reason}")

        # Reset step-specific state variables
        self.step_elements = []
        self.step_matches = {}
        self.element_matches = {}
        self.test_data = {}
        self.manual_test_data = {}  # Reset manually entered test data
        self.test_data_skipped = False
        self.llm_step_analysis = {}
        self.step_ready_for_script = False
        self.script_just_generated = False
        self.generated_script_path = None

        # Reset validation state
        self.script_validation_done = False
        self.script_validation_passed = False
        self.script_validation_results = {}

        # Reset regeneration attempts counter
        self.regen_attempts = 0

        # Reset error state
        self.execution_error_occurred = False
        self.execution_error_acknowledged = False
        self.execution_error_details = {}

        return True

    def reset_test_case_state(self, confirm=False, reason=""):
        """
        Reset test case state variables.

        This method provides a centralized way to reset test case state variables,
        ensuring that all resets are properly validated, logged, and confirmed by the user.

        Args:
            confirm: Whether the reset has been confirmed by the user
            reason: The reason for the reset (for logging)

        Returns:
            bool: True if state was reset, False otherwise
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if not confirm:
            logger.warning(f"Attempted to reset test case state without confirmation: {reason}")
            return False

        logger.info(f"Resetting test case state: {reason}")

        # Reset test case state variables
        self.selected_test_case = None
        self.original_test_case = None
        self.selected_step = None  # Clear selected step as part of test case reset
        self.selected_step_table_entry = None  # Clear step table entry as well
        self.step_table_markdown = ""
        self.step_table_json = []
        self.step_table_analysis = None
        self.conversion_done = False

        # Also reset step state
        self.reset_step_state(confirm=True, reason=f"Part of test case reset: {reason}")

        # Reset step progression counters
        self.current_step_index = 0
        self.total_steps = 0
        self.all_steps_done = False
        self.completed_steps = []
        self.step_context = {}

        # Reset script continuity tracking
        self.combined_script_content = None
        self.combined_script_path = None
        self.script_imports = []
        self.script_fixtures = []
        self.script_variables = {}
        self.script_functions = {}
        self.browser_initialized = False
        self.previous_scripts = {}

        # Reset Stage 8 optimization state
        self.optimized_script_path = None
        self.optimized_script_content = ""
        self.optimization_in_progress = False
        self.optimization_complete = False
        self.optimization_start_time = None
        self.optimization_chunks = []
        self.optimized_script_test_results = None  # Test results for optimized script

        # Reset Stage 8 validation and comment enhancement state
        self.optimized_script_validation_results = {}
        self.optimized_script_validation_done = False
        self.user_optimization_comment = ""
        self.ai_enhanced_comment = ""
        self.comment_enhancement_done = False
        self.use_enhanced_comment = False
        self.regeneration_custom_instructions = None

        # Reset current stage to Stage 3 (test case selection) using centralized method
        self.advance_to(StateStage.STAGE3_CONVERT, f"Test case reset: {reason}")

        return True

    def advance_to(self, new_stage: StateStage, reason: str = "") -> bool:
        """
        Centralized stage transition method with validation and comprehensive flag cleanup.

        This method serves as the single source of truth for stage transitions,
        replacing the fragile distributed boolean flag manipulation that causes phantom stage jumps.

        Args:
            new_stage: The target stage to transition to
            reason: The reason for the transition (for logging)

        Returns:
            bool: True if transition was successful, False if invalid
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Validate transition is legal
        current_stage_num = self.current_stage.get_stage_number()
        target_stage_num = new_stage.get_stage_number()

        # Stage 9 (Script Browser) is always accessible from any stage
        if target_stage_num == 9:
            logger.info(f"Allowing transition to Stage 9 (Script Browser) from Stage {current_stage_num}")
            # Update the authoritative current stage
            self.current_stage = new_stage
            logger.info(f"State change: current_stage = {self.current_stage.get_display_name()}")
            return True

        # Define legal transitions (can go forward, or specific backward transitions)
        legal_backward_transitions = {
            # Stage 7 -> Stage 4 (after script execution, return to step selection)
            (7, 4): "Script execution completed, returning to step selection",
            # Stage 8 -> Stage 3 (after optimization, return to test case selection)
            (8, 3): "Script optimization completed, returning to test case selection",
            # Stage 9 -> Any stage (from script browser to any workflow stage)
            (9, 1): "Script browser navigation to Stage 1",
            (9, 2): "Script browser navigation to Stage 2",
            (9, 3): "Script browser navigation to Stage 3",
            (9, 4): "Script browser navigation to Stage 4",
            (9, 5): "Script browser navigation to Stage 5",
            (9, 6): "Script browser navigation to Stage 6",
            (9, 7): "Script browser navigation to Stage 7",
            (9, 8): "Script browser navigation to Stage 8",
            # Stage 4 -> Stage 4 (step navigation within Stage 4)
            (4, 4): "Step navigation within Stage 4",
            # Any stage -> Stage 3 (test case reset)
            (4, 3): "Test case reset from Stage 4",
            (5, 3): "Test case reset from Stage 5",
            (6, 3): "Test case reset from Stage 6",
            (7, 3): "Test case reset from Stage 7",
            (8, 3): "Test case reset from Stage 8",
            # Any stage -> Stage 9 (script browser access)
            (1, 9): "Access script browser from Stage 1",
            (2, 9): "Access script browser from Stage 2",
            (3, 9): "Access script browser from Stage 3",
            (4, 9): "Access script browser from Stage 4",
            (5, 9): "Access script browser from Stage 5",
            (6, 9): "Access script browser from Stage 6",
            (7, 9): "Access script browser from Stage 7",
            (8, 9): "Access script browser from Stage 8"
        }

        # Check if transition is legal
        is_forward = target_stage_num > current_stage_num
        is_same_stage = target_stage_num == current_stage_num
        is_legal_backward = (current_stage_num, target_stage_num) in legal_backward_transitions
        is_complete_reset = target_stage_num == 1 and current_stage_num >= 4  # Only allow reset from Stage 4 and beyond

        if not (is_forward or is_same_stage or is_legal_backward or is_complete_reset):
            logger.error(f"Illegal stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.error(f"Legal transitions from Stage {current_stage_num}: forward to {current_stage_num + 1}-8, or specific backward transitions")
            return False

        # Log the transition
        if is_legal_backward:
            transition_reason = legal_backward_transitions.get((current_stage_num, target_stage_num), reason)
            logger.info(f"Legal backward stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Transition reason: {transition_reason}")
        elif is_complete_reset:
            logger.info(f"Complete application reset: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Reset reason: {reason}")
        else:
            logger.info(f"Stage transition: {self.current_stage.get_display_name()} -> {new_stage.get_display_name()}")
            logger.info(f"Transition reason: {reason}")

        # Store previous stage for cleanup logic
        previous_stage = self.current_stage

        # Update the authoritative current stage
        self.current_stage = new_stage
        logger.info(f"State change: current_stage = {self.current_stage.get_display_name()}")

        # Perform stage-specific cleanup based on transition type
        self._cleanup_flags_for_stage_transition(previous_stage, new_stage, reason)

        return True

    def _cleanup_flags_for_stage_transition(self, previous_stage: StateStage, target_stage: StateStage, reason: str):
        """
        Clean up boolean flags that could interfere with stage detection.

        This method ensures that stale flags from previous stages don't cause
        phantom stage jumps by systematically clearing flags that are no longer relevant.

        Args:
            previous_stage: The stage we're transitioning from
            target_stage: The stage we're transitioning to
            reason: The reason for the transition (for logging)
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        logger.info(f"Cleaning up flags for transition: {previous_stage.get_display_name()} -> {target_stage.get_display_name()}")

        target_stage_num = target_stage.get_stage_number()

        # Clear flags that are ahead of the target stage to prevent phantom jumps
        if target_stage_num < 3:
            # Going back to Stage 1 or 2 - clear all test case related flags
            if self.conversion_done:
                self.conversion_done = False
                logger.info("State change: conversion_done = False (stage cleanup)")

        if target_stage_num < 4:
            # Going back before Stage 4 - clear step selection flags
            if self.step_ready_for_script:
                self.step_ready_for_script = False
                logger.info("State change: step_ready_for_script = False (stage cleanup)")

        if target_stage_num < 5:
            # Going back before Stage 5 - clear element matching flags
            if self.step_matches:
                self.step_matches = {}
                logger.info("State change: step_matches = {} (stage cleanup)")
            if self.element_matches:
                self.element_matches = {}
                logger.info("State change: element_matches = {} (stage cleanup)")

        if target_stage_num < 6:
            # Going back before Stage 6 - clear test data flags
            if self.test_data:
                self.test_data = {}
                logger.info("State change: test_data = {} (stage cleanup)")
            if self.test_data_skipped:
                self.test_data_skipped = False
                logger.info("State change: test_data_skipped = False (stage cleanup)")

        if target_stage_num < 7:
            # Going back before Stage 7 - clear script generation flags
            if self.generated_script_path:
                self.generated_script_path = None
                logger.info("State change: generated_script_path = None (stage cleanup)")
            if self.script_just_generated:
                self.script_just_generated = False
                logger.info("State change: script_just_generated = False (stage cleanup)")
            if self.script_validation_done:
                self.script_validation_done = False
                logger.info("State change: script_validation_done = False (stage cleanup)")
            if self.script_validation_passed:
                self.script_validation_passed = False
                logger.info("State change: script_validation_passed = False (stage cleanup)")

        if target_stage_num < 8:
            # Going back before Stage 8 - clear optimization flags
            if self.all_steps_done:
                self.all_steps_done = False
                logger.info("State change: all_steps_done = False (stage cleanup)")
            if self.optimization_in_progress:
                self.optimization_in_progress = False
                logger.info("State change: optimization_in_progress = False (stage cleanup)")
            if self.optimization_complete:
                self.optimization_complete = False
                logger.info("State change: optimization_complete = False (stage cleanup)")

        # Special case: Stage 7 -> Stage 4 transition (step advancement)
        if previous_stage == StateStage.STAGE7_EXECUTE and target_stage == StateStage.STAGE4_DETECT:
            # Don't clear test case level flags, only step-specific ones
            logger.info("Stage 7 -> Stage 4 transition: preserving test case state, clearing step state")
            # The reset_step_state method will be called separately

        # Special case: Stage 8 -> Stage 3 transition (new test case)
        elif previous_stage == StateStage.STAGE8_OPTIMIZE and target_stage == StateStage.STAGE3_CONVERT:
            # Clear all test case state for fresh start
            logger.info("Stage 8 -> Stage 3 transition: clearing all test case state")
            # The reset_test_case_state method will be called separately

        # Special case: Stage 9 -> Stage 8 transition (back to optimization)
        elif previous_stage == StateStage.STAGE9_BROWSE and target_stage == StateStage.STAGE8_OPTIMIZE:
            # Preserve all optimization state when returning from script browser
            logger.info("Stage 9 -> Stage 8 transition: preserving optimization state")
            # No cleanup needed - user is returning to continue optimization work

        logger.info(f"Flag cleanup completed for {previous_stage.get_display_name()} -> {target_stage.get_display_name()}")

    def update_stage_based_on_completion(self) -> bool:
        """
        Update the current stage based on completion criteria.

        This method analyzes the current state and determines the appropriate stage
        based on what has been completed. It serves as a fallback for cases where
        the stage might get out of sync with the actual progress.

        Returns:
            bool: True if stage was updated, False if no change needed
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Store current stage for comparison
        original_stage = self.current_stage

        # Determine the appropriate stage based on completion criteria
        target_stage = StateStage.STAGE1_UPLOAD  # Default to Stage 1

        # Add validation logging to understand why we might default to Stage 1
        logger.debug(f"Stage validation - uploaded_excel: {bool(getattr(self, 'uploaded_excel', None))}")
        logger.debug(f"Stage validation - uploaded_file: {bool(getattr(self, 'uploaded_file', None))}")
        logger.debug(f"Stage validation - website_url: {getattr(self, 'website_url', 'None')}")
        logger.debug(f"Stage validation - selected_test_case: {bool(getattr(self, 'selected_test_case', None))}")
        logger.debug(f"Stage validation - conversion_done: {bool(getattr(self, 'conversion_done', None))}")
        logger.debug(f"Stage validation - step_table_json: {bool(getattr(self, 'step_table_json', None))}")

        # Check Stage 1 completion (file uploaded)
        if self.uploaded_excel or self.uploaded_file:
            target_stage = StateStage.STAGE2_WEBSITE

            # Check Stage 2 completion (website URL configured)
            if self.website_url and self.website_url != "https://example.com":
                target_stage = StateStage.STAGE3_CONVERT

                # Check Stage 3 completion (test case selected and converted)
                if self.selected_test_case and self.conversion_done and self.step_table_json:
                    target_stage = StateStage.STAGE4_DETECT

                    # Check Stage 4 completion (step selected and elements matched)
                    if self.selected_step and (self.step_matches or self.element_matches):
                        target_stage = StateStage.STAGE5_DATA

                        # Check Stage 5 completion (test data configured or skipped)
                        if self.test_data or self.test_data_skipped:
                            target_stage = StateStage.STAGE6_GENERATE

                            # Check Stage 6 completion (script generated)
                            if self.generated_script_path:
                                target_stage = StateStage.STAGE7_EXECUTE

                                # Check Stage 7 completion (all steps done)
                                if self.all_steps_done:
                                    target_stage = StateStage.STAGE8_OPTIMIZE

        # Update stage if different from current
        if target_stage != self.current_stage:
            # Add safety check: don't auto-revert to Stage 1 unless we're sure it's correct
            if target_stage == StateStage.STAGE1_UPLOAD and original_stage.get_stage_number() > 1:
                # Only revert to Stage 1 if we have clear evidence that earlier stages are incomplete
                has_file = bool(getattr(self, 'uploaded_excel', None) or getattr(self, 'uploaded_file', None))
                has_test_cases = bool(getattr(self, 'test_cases', None))

                if has_file and has_test_cases:
                    # We have file and test cases, don't revert to Stage 1
                    logger.warning(f"Preventing auto-revert to Stage 1: file and test cases exist")
                    logger.warning(f"Keeping current stage: {self.current_stage.get_display_name()}")
                    return False
                else:
                    logger.warning(f"Auto-reverting to Stage 1: missing file ({has_file}) or test cases ({has_test_cases})")

            logger.info(f"Auto-updating stage based on completion: {self.current_stage.get_display_name()} -> {target_stage.get_display_name()}")
            self.current_stage = target_stage
            logger.info(f"State change: current_stage = {self.current_stage.get_display_name()} (auto-update)")
            return True

        return False

    @property
    def current_app_stage(self) -> int:
        """
        Backward compatibility property that returns the current stage as an integer.

        This property allows existing code that expects current_app_stage to continue working
        while using the new centralized stage management system.

        Returns:
            int: Current stage number (1-8)
        """
        return self.current_stage.get_stage_number()

    def update_script_continuity(self, script: str, step_no: str):
        """
        Store the generated (or merged) script and extract key elements for continuity.

        Args:
            script: The script content to analyze and store
            step_no: The step number this script belongs to
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Store the complete script
        self.previous_scripts[step_no] = script
        logger.info(f"Stored script for step {step_no} (length: {len(script)} chars)")

        # Extract imports, fixtures, functions and variables
        script_lines = script.splitlines()
        for line in script_lines:
            line_stripped = line.strip()

            # Track imports
            if line_stripped.startswith(('import ', 'from ')):
                if line_stripped not in self.script_imports:
                    self.script_imports.append(line_stripped)
                    logger.info(f"Added import: {line_stripped}")

            # Track fixtures
            elif line_stripped.startswith('@pytest.fixture'):
                # Extract fixture name from the next line (assumes fixture definition follows decorator)
                next_line_idx = script.splitlines().index(line) + 1
                if next_line_idx < len(script.splitlines()):
                    next_line = script.splitlines()[next_line_idx]
                    if 'def ' in next_line:
                        fn = next_line.split('def')[-1].split('(')[0].strip()
                        if fn not in [f.split()[-1] for f in self.script_fixtures]:
                            self.script_fixtures.append(line_stripped)
                            logger.info(f"Added fixture: {fn}")

            # Track helper functions (non-test functions)
            elif line_stripped.startswith('def ') and not line_stripped.startswith('def test_'):
                fn = line_stripped.split()[1].split('(')[0]
                if fn not in self.script_functions:
                    self.script_functions[fn] = line_stripped
                    logger.info(f"Added helper function: {fn}")

            # Track variable definitions
            elif '=' in line_stripped and not line_stripped.startswith(('#', '/', 'if', 'for', 'while', 'def')):
                var_parts = line_stripped.split('=', 1)
                var_name = var_parts[0].strip()
                var_value = var_parts[1].strip()
                if var_name and not var_name.startswith(('_', 'test_')) and var_name not in ['driver']:
                    self.script_variables[var_name] = var_value
                    logger.info(f"Added variable: {var_name} = {var_value}")

        # Check if browser is initialized
        if not self.browser_initialized and any(browser_init in script for browser_init in
                                             ['driver = webdriver.Chrome(', 'driver = webdriver.Firefox(', 'driver = webdriver.Edge(']):
            self.browser_initialized = True
            logger.info("Browser initialization detected")

        return True

    def add_script_to_history(self, script_content: str, script_type: str, step_no: str = None, file_path: str = None, metadata: Dict[str, Any] = None):
        """
        Add a script to the script history for Stage 9 browsing.

        Args:
            script_content: The script content
            script_type: Type of script ('step', 'combined', 'optimized')
            step_no: Step number (for step scripts)
            file_path: Path to the script file
            metadata: Additional metadata about the script
        """
        import logging
        from datetime import datetime
        logger = logging.getLogger("ScriptWeaver.state_manager")

        # Create script entry
        script_entry = {
            'id': f"{script_type}_{step_no or 'all'}_{int(datetime.now().timestamp())}",
            'content': script_content,
            'type': script_type,
            'step_no': step_no,
            'file_path': file_path,
            'timestamp': datetime.now(),
            'test_case_id': self.selected_test_case.get('Test Case ID', 'unknown') if self.selected_test_case else 'unknown',
            'metadata': metadata or {}
        }

        # Add to history
        self.script_history.append(script_entry)

        # Store metadata separately for quick access
        self.script_metadata[script_entry['id']] = {
            'type': script_type,
            'step_no': step_no,
            'timestamp': script_entry['timestamp'],
            'test_case_id': script_entry['test_case_id'],
            'file_size': len(script_content),
            'line_count': len(script_content.splitlines()),
            'metadata': metadata or {}
        }

        logger.info(f"Added {script_type} script to history: {script_entry['id']}")

        # Also save to persistent storage
        self._save_to_persistent_storage(script_content, script_type, step_no, file_path, metadata)

    def _init_script_storage(self):
        """Initialize persistent script storage."""
        try:
            from core.script_storage import get_script_storage
            self._script_storage = get_script_storage()

            # Load historical scripts into current session
            self._load_historical_scripts()

            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.info("Initialized persistent script storage")
        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to initialize script storage: {e}")
            self._script_storage = None

    def _save_to_persistent_storage(self, script_content: str, script_type: str, step_no: str = None,
                                   file_path: str = None, metadata: Dict[str, Any] = None):
        """Save script to persistent storage."""
        if self._script_storage is None:
            return

        try:
            test_case_id = None
            if hasattr(self, 'selected_test_case') and self.selected_test_case:
                test_case_id = self.selected_test_case.get('Test Case ID', 'unknown')

            script_id = self._script_storage.save_script(
                script_content=script_content,
                script_type=script_type,
                test_case_id=test_case_id,
                step_no=step_no,
                file_path=file_path,
                metadata=metadata
            )

            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.info(f"Saved script to persistent storage: {script_id}")

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to save script to persistent storage: {e}")

    def _load_historical_scripts(self):
        """Load historical scripts from persistent storage."""
        if self._script_storage is None:
            return

        try:
            # Get all scripts from storage
            historical_scripts = self._script_storage.get_all_scripts(include_current_session=True)

            # Convert to the format expected by script_history
            for script in historical_scripts:
                # Check if script is already in current session history
                existing_script = next(
                    (s for s in self.script_history if s.get('id') == script['id']),
                    None
                )

                if existing_script is None:
                    # Add to script history
                    self.script_history.append(script)

                    # Add to metadata
                    self.script_metadata[script['id']] = {
                        'type': script['type'],
                        'step_no': script['step_no'],
                        'timestamp': script['timestamp'],
                        'test_case_id': script['test_case_id'],
                        'file_size': script['file_size'],
                        'line_count': script['line_count'],
                        'metadata': script.get('metadata', {})
                    }

            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.info(f"Loaded {len(historical_scripts)} historical scripts")

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to load historical scripts: {e}")

    def get_all_scripts_with_history(self) -> List[Dict[str, Any]]:
        """
        Get all scripts including historical ones from persistent storage.

        Returns:
            List of all scripts with complete history
        """
        if self._script_storage is None:
            return self.script_history

        try:
            return self._script_storage.get_all_scripts(include_current_session=True)
        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to get scripts with history: {e}")
            return self.script_history

    def get_script_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about all scripts including historical ones.

        Returns:
            Dictionary containing script statistics
        """
        if self._script_storage is None:
            # Fallback to in-memory statistics
            from core.script_browser_helpers import get_script_statistics
            return get_script_statistics(self.script_history)

        try:
            return self._script_storage.get_script_statistics()
        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Failed to get script statistics: {e}")
            # Fallback to in-memory statistics
            from core.script_browser_helpers import get_script_statistics
            return get_script_statistics(self.script_history)

    def can_access_stage(self, target_stage: StateStage) -> bool:
        """
        Check if a stage can be accessed based on current state and prerequisites.

        Args:
            target_stage: The stage to check access for

        Returns:
            bool: True if stage is accessible, False otherwise
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('accessible', False)

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Error checking stage accessibility: {e}")
            return False

    def get_stage_prerequisites(self, target_stage: StateStage) -> List[str]:
        """
        Get the list of missing prerequisites for a stage.

        Args:
            target_stage: The stage to check prerequisites for

        Returns:
            List of missing prerequisite descriptions
        """
        try:
            # Import navigation helpers to avoid circular imports
            from core.navigation_helpers import get_stage_accessibility

            accessibility_info = get_stage_accessibility(self, target_stage)
            return accessibility_info.get('missing_prerequisites', [])

        except Exception as e:
            import logging
            logger = logging.getLogger("ScriptWeaver.state_manager")
            logger.error(f"Error getting stage prerequisites: {e}")
            return ['Error checking prerequisites']

    def add_validation_feedback(self, validation_results: Dict[str, Any], test_case_id: str, step_no: str):
        """
        Add validation feedback to the learning history.

        This method collects validation results and common issues to improve future script generation.

        Args:
            validation_results: The validation results from script validation
            test_case_id: The test case ID for context
            step_no: The step number for context
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        try:
            # Extract key feedback information
            feedback_entry = {
                'timestamp': datetime.now().isoformat(),
                'test_case_id': test_case_id,
                'step_no': step_no,
                'quality_score': validation_results.get('quality_score', 0),
                'issues_found': validation_results.get('issues_found', []),
                'recommendations': validation_results.get('recommendations', []),
                'confidence_rating': validation_results.get('confidence_rating', 'medium'),
                'ready_for_execution': validation_results.get('ready_for_execution', True)
            }

            # Add to feedback history
            self.validation_feedback_history.append(feedback_entry)

            # Keep only the last 50 feedback entries to prevent memory bloat
            if len(self.validation_feedback_history) > 50:
                self.validation_feedback_history = self.validation_feedback_history[-50:]

            logger.info(f"Added validation feedback for {test_case_id} step {step_no} (quality: {feedback_entry['quality_score']})")

        except Exception as e:
            logger.error(f"Error adding validation feedback: {e}")

    def get_common_validation_issues(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Analyze validation feedback history to identify common issues.

        Args:
            limit: Maximum number of common issues to return

        Returns:
            List of common issues with frequency counts
        """
        import logging
        from collections import Counter

        logger = logging.getLogger("ScriptWeaver.state_manager")

        try:
            if not self.validation_feedback_history:
                return []

            # Collect all issues from feedback history
            all_issues = []
            all_recommendations = []

            for feedback in self.validation_feedback_history:
                # Extract issue categories and descriptions
                for issue in feedback.get('issues_found', []):
                    category = issue.get('category', 'unknown')
                    severity = issue.get('severity', 'medium')
                    description = issue.get('description', '')
                    all_issues.append(f"{category}:{severity}:{description}")

                # Extract recommendations
                for rec in feedback.get('recommendations', []):
                    all_recommendations.append(rec)

            # Count frequency of issues and recommendations
            issue_counter = Counter(all_issues)
            rec_counter = Counter(all_recommendations)

            # Combine and format results
            common_issues = []

            # Add most common issues with severity weighting
            for issue, count in issue_counter.most_common(limit // 2):
                parts = issue.split(':', 2)
                if len(parts) == 3:
                    severity = parts[1]
                    # Calculate severity weight for sorting (high=3, medium=2, low=1)
                    severity_weight = 3 if severity == 'high' else 2 if severity == 'medium' else 1

                    common_issues.append({
                        'type': 'issue',
                        'category': parts[0],
                        'severity': severity,
                        'description': parts[2],
                        'frequency': count,
                        'severity_weight': severity_weight
                    })

            # Add most common recommendations with default weighting
            for rec, count in rec_counter.most_common(limit // 2):
                common_issues.append({
                    'type': 'recommendation',
                    'category': 'improvement',
                    'severity': 'medium',
                    'description': rec,
                    'frequency': count,
                    'severity_weight': 2  # Default weight for recommendations
                })

            logger.info(f"Identified {len(common_issues)} common validation issues from {len(self.validation_feedback_history)} feedback entries")
            return common_issues

        except Exception as e:
            logger.error(f"Error analyzing common validation issues: {e}")
            return []

    def track_script_regeneration(self, reason: str = "validation_feedback"):
        """
        Track script regeneration attempts for feedback loop analysis.

        Args:
            reason: The reason for regeneration (e.g., 'validation_feedback', 'user_request')
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        try:
            self.script_regeneration_count += 1

            # Add regeneration context to the most recent feedback entry if available
            if self.validation_feedback_history:
                latest_feedback = self.validation_feedback_history[-1]
                if 'regeneration_triggered' not in latest_feedback:
                    latest_feedback['regeneration_triggered'] = True
                    latest_feedback['regeneration_reason'] = reason
                    latest_feedback['regeneration_count'] = self.script_regeneration_count

            logger.info(f"Script regeneration tracked: count={self.script_regeneration_count}, reason={reason}")

        except Exception as e:
            logger.error(f"Error tracking script regeneration: {e}")

    def get_feedback_effectiveness_metrics(self) -> Dict[str, Any]:
        """
        Calculate metrics to assess feedback loop effectiveness.

        Returns:
            Dict containing effectiveness metrics
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        try:
            if not self.validation_feedback_history:
                return {
                    'total_feedback_entries': 0,
                    'regeneration_rate': 0.0,
                    'improvement_trend': 'insufficient_data',
                    'common_issue_categories': []
                }

            total_entries = len(self.validation_feedback_history)
            regenerations = sum(1 for entry in self.validation_feedback_history if entry.get('regeneration_triggered', False))
            regeneration_rate = regenerations / total_entries if total_entries > 0 else 0.0

            # Calculate quality score trend (last 5 vs first 5 entries)
            quality_scores = [entry.get('quality_score', 0) for entry in self.validation_feedback_history if entry.get('quality_score', 0) > 0]
            improvement_trend = 'insufficient_data'

            if len(quality_scores) >= 6:
                early_avg = sum(quality_scores[:3]) / 3
                recent_avg = sum(quality_scores[-3:]) / 3
                if recent_avg > early_avg + 5:
                    improvement_trend = 'improving'
                elif recent_avg < early_avg - 5:
                    improvement_trend = 'declining'
                else:
                    improvement_trend = 'stable'

            # Get most common issue categories
            from collections import Counter
            all_categories = []
            for entry in self.validation_feedback_history:
                for issue in entry.get('issues_found', []):
                    all_categories.append(issue.get('category', 'unknown'))

            common_categories = [cat for cat, count in Counter(all_categories).most_common(5)]

            metrics = {
                'total_feedback_entries': total_entries,
                'regeneration_count': self.script_regeneration_count,
                'regeneration_rate': round(regeneration_rate, 2),
                'improvement_trend': improvement_trend,
                'common_issue_categories': common_categories,
                'avg_quality_score': round(sum(quality_scores) / len(quality_scores), 1) if quality_scores else 0
            }

            logger.info(f"Feedback effectiveness metrics: {metrics}")
            return metrics

        except Exception as e:
            logger.error(f"Error calculating feedback effectiveness metrics: {e}")
            return {
                'total_feedback_entries': len(self.validation_feedback_history),
                'regeneration_rate': 0.0,
                'improvement_trend': 'error',
                'common_issue_categories': [],
                'error': str(e)
            }

    def set_execution_error(self, error_details: Dict[str, Any]):
        """
        Set execution error state with detailed error information.

        Args:
            error_details: Dictionary containing error information including:
                - error_message: Main error message
                - traceback: Full traceback if available
                - returncode: Exit code from script execution
                - stdout: Standard output from execution
                - stderr: Standard error from execution
                - timestamp: When the error occurred
                - step_no: Which step failed
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        self.execution_error_occurred = True
        self.execution_error_acknowledged = False
        self.execution_error_details = error_details.copy()

        logger.info(f"State change: execution_error_occurred = True for step {error_details.get('step_no', 'Unknown')}")
        logger.error(f"Execution error details: {error_details.get('error_message', 'No message')}")

    def acknowledge_execution_error(self):
        """
        Mark the current execution error as acknowledged by the user.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.execution_error_occurred and not self.execution_error_acknowledged:
            self.execution_error_acknowledged = True
            logger.info("State change: execution_error_acknowledged = True")
            return True
        return False

    def clear_execution_error(self):
        """
        Clear execution error state after user acknowledgment.
        """
        import logging
        logger = logging.getLogger("ScriptWeaver.state_manager")

        if self.execution_error_occurred:
            logger.info("State change: clearing execution error state")
            self.execution_error_occurred = False
            self.execution_error_acknowledged = False
            self.execution_error_details = {}
            return True
        return False