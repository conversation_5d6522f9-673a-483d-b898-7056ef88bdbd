"""
Stage 9: Script Browser and Comparison for GretahAI ScriptWeaver

This module provides an always-accessible script browsing and comparison utility.
It provides functionality for:
- Browsing all generated scripts from current and previous sessions
- Comparing different script versions (original vs optimized)
- Displaying script metadata and generation history
- Searching and filtering scripts by various criteria
- Downloading any script version
- Side-by-side diff views with syntax highlighting

Key Features:
- **Always Accessible**: Can be accessed at any time, regardless of workflow stage
- **Session Independent**: Browse scripts from previous work sessions
- **Empty State Handling**: Graceful UI when no scripts exist with helpful navigation
- **Utility Nature**: Functions as a standalone tool rather than a workflow step

The stage maintains the established architectural patterns:
- StateManager pattern with logging and st.rerun() after state changes
- Collapsible sections using st.expander() with expanded=True
- Manual progression with confirmation buttons
- Minimalist UI design with minimal instructional text
- Proper workflow transitions for quick navigation

Functions:
    stage9_browse_scripts(state): Main Stage 9 function for script browsing and comparison
"""

import os
import logging
import streamlit as st
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from state_manager import StateStage

# Import helper functions
from core.script_browser_helpers import (
    filter_scripts_by_criteria,
    search_scripts_by_content,
    generate_script_comparison,
    format_script_metadata,
    get_script_statistics,
    create_download_filename
)

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage9")


def stage9_browse_scripts(state):
    """
    Stage 9: Script Browser and Comparison.

    This stage provides an independent, always-accessible script browsing and comparison utility.
    It loads scripts from persistent storage across all sessions, making it truly session-independent.
    Users can access this stage at any time regardless of workflow completion status.

    Args:
        state (StateManager): The application state manager instance
    """
    st.markdown("<h2 class='stage-header'>📜 Script Browser & History</h2>", unsafe_allow_html=True)
    st.markdown("*Browse and compare all generated scripts from current and previous sessions*")

    # Get all scripts including historical ones from persistent storage
    try:
        all_scripts = state.get_all_scripts_with_history()
    except Exception as e:
        logger.error(f"Failed to load scripts: {e}")
        all_scripts = state.script_history  # Fallback to session scripts

    # Check if we have any scripts in history
    if not all_scripts:
        st.info("📝 **Script Browser is Empty**")
        st.markdown("""
        **Welcome to the Script Browser!** This utility allows you to browse, compare, and download all generated test scripts.

        **To get started:**
        - 📁 Upload a CSV file and configure your test case (Stages 1-3)
        - 🔍 Detect UI elements and configure test data (Stages 4-5)
        - ⚙️ Generate your first test script (Stage 6+)

        **What you can do here:**
        - 🔍 Browse and search through all generated scripts
        - 📊 Compare different script versions side-by-side
        - 📥 Download scripts for external use
        - 📈 View script generation history and metadata
        """)

        # Provide navigation to start the workflow
        st.markdown("### 🚀 Quick Start")
        col1, col2, col3 = st.columns(3)
        with col1:
            if st.button("📁 Start New Project", use_container_width=True, type="primary"):
                state.advance_to(StateStage.STAGE1_UPLOAD, "User started new project from Script Browser")
                st.rerun()
                return
        with col2:
            if st.button("🎯 Select Test Case", use_container_width=True):
                state.advance_to(StateStage.STAGE3_CONVERT, "User navigated to test case selection from Script Browser")
                st.rerun()
                return
        with col3:
            if st.button("⚙️ Generate Scripts", use_container_width=True):
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to script generation from Script Browser")
                st.rerun()
                return
        return

    # Display script statistics overview
    with st.expander("📊 Script History Overview", expanded=True):
        try:
            stats = state.get_script_statistics()
        except Exception as e:
            logger.error(f"Failed to get script statistics: {e}")
            stats = get_script_statistics(all_scripts)

        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Scripts", stats.get('total_scripts', 0))
        with col2:
            st.metric("Test Cases", len(stats.get('test_cases', {})))
        with col3:
            st.metric("Total Lines", f"{stats.get('total_lines', 0):,}")
        with col4:
            st.metric("Total Size", f"{stats.get('total_size', 0):,} chars")

        # Script type breakdown
        script_types = stats.get('script_types', {})
        if script_types:
            st.markdown("**Script Types:**")
            type_cols = st.columns(len(script_types))
            for i, (script_type, count) in enumerate(script_types.items()):
                with type_cols[i]:
                    st.metric(script_type.title(), count)

        # Show date range if available
        if stats.get('date_range'):
            start_date, end_date = stats['date_range']
            st.markdown(f"**Date Range:** {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")

        # Show session info
        current_session_count = len([s for s in all_scripts if s.get('session_id') == getattr(state._script_storage, 'session_id', None)])
        historical_count = len(all_scripts) - current_session_count
        if historical_count > 0:
            st.info(f"📚 Showing {current_session_count} scripts from current session and {historical_count} from previous sessions")

    # Filtering and Search Section
    with st.expander("🔍 Filter and Search Scripts", expanded=False):
        col1, col2 = st.columns(2)

        with col1:
            # Filter options
            st.markdown("**Filter Options:**")

            # Script type filter
            script_types = list(set(script.get('type', 'unknown') for script in all_scripts))
            selected_type = st.selectbox(
                "Script Type",
                options=['All'] + script_types,
                index=0,
                key="filter_script_type"
            )

            # Test case filter
            test_cases = list(set(script.get('test_case_id', 'unknown') for script in all_scripts if script.get('test_case_id')))
            selected_test_case = st.selectbox(
                "Test Case",
                options=['All'] + test_cases,
                index=0,
                key="filter_test_case"
            )

            # Step number filter
            step_numbers = list(set(script.get('step_no', '') for script in all_scripts if script.get('step_no')))
            step_numbers = [s for s in step_numbers if s]  # Remove empty strings
            selected_step = st.selectbox(
                "Step Number",
                options=['All'] + sorted(step_numbers),
                index=0,
                key="filter_step_no"
            )

        with col2:
            # Search options
            st.markdown("**Search Options:**")

            search_term = st.text_input(
                "Search in script content",
                placeholder="Enter search term...",
                key="search_term"
            )

            case_sensitive = st.checkbox(
                "Case sensitive search",
                key="case_sensitive_search"
            )

            # Date range filter
            st.markdown("**Date Range:**")
            date_filter = st.selectbox(
                "Filter by date",
                options=['All time', 'Last hour', 'Last 24 hours', 'Last week'],
                index=0,
                key="date_filter"
            )

    # Apply filters and search
    filtered_scripts = all_scripts.copy()

    # Apply type filter
    if selected_type != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            script_type=selected_type
        )

    # Apply test case filter
    if selected_test_case != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            test_case_id=selected_test_case
        )

    # Apply step filter
    if selected_step != 'All':
        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            step_no=selected_step
        )

    # Apply date filter
    if date_filter != 'All time':
        now = datetime.now()
        if date_filter == 'Last hour':
            start_date = now - timedelta(hours=1)
        elif date_filter == 'Last 24 hours':
            start_date = now - timedelta(days=1)
        elif date_filter == 'Last week':
            start_date = now - timedelta(weeks=1)
        else:
            start_date = datetime.min

        filtered_scripts = filter_scripts_by_criteria(
            filtered_scripts,
            date_range=(start_date, now)
        )

    # Apply search
    if search_term:
        filtered_scripts = search_scripts_by_content(
            filtered_scripts,
            search_term,
            case_sensitive
        )

    # Display filtered results count
    if len(filtered_scripts) != len(all_scripts):
        st.info(f"📋 Showing {len(filtered_scripts)} of {len(all_scripts)} scripts")

    # Script Browser Section
    with st.expander("📜 Script Browser", expanded=True):
        if not filtered_scripts:
            st.warning("No scripts match the current filters.")
        else:
            # Sort scripts by timestamp (newest first)
            sorted_scripts = sorted(filtered_scripts, key=lambda x: x.get('timestamp', datetime.min), reverse=True)

            for i, script in enumerate(sorted_scripts):
                with st.container():
                    # Script header with metadata
                    col1, col2, col3 = st.columns([3, 1, 1])

                    with col1:
                        script_title = f"{script.get('type', 'Script').title()} - {script.get('test_case_id', 'Unknown')}"
                        if script.get('step_no'):
                            script_title += f" (Step {script.get('step_no')})"
                        st.markdown(f"**{script_title}**")

                        timestamp = script.get('timestamp', datetime.now())
                        st.caption(f"Generated: {timestamp.strftime('%Y-%m-%d %H:%M:%S')}")

                    with col2:
                        # Download button
                        filename = create_download_filename(script)
                        if st.download_button(
                            "📥 Download",
                            data=script.get('content', ''),
                            file_name=filename,
                            mime="text/plain",
                            key=f"download_{script.get('id', i)}"
                        ):
                            st.success(f"✅ Downloaded {filename}")

                    with col3:
                        # View/Compare toggle
                        view_key = f"view_{script.get('id', i)}"
                        if st.button("👁️ View", key=view_key):
                            st.session_state[f"show_script_{i}"] = not st.session_state.get(f"show_script_{i}", False)

                    # Show script content if toggled
                    if st.session_state.get(f"show_script_{i}", False):
                        # Metadata display
                        metadata = format_script_metadata(script)

                        meta_cols = st.columns(3)
                        meta_items = list(metadata.items())
                        for j, (key, value) in enumerate(meta_items):
                            with meta_cols[j % 3]:
                                st.metric(key, value)

                        # Script content with syntax highlighting
                        st.code(script.get('content', ''), language="python")

                    st.divider()

    # Script Comparison Section
    with st.expander("🔄 Script Comparison", expanded=False):
        if len(filtered_scripts) < 2:
            st.info("Need at least 2 scripts to perform comparison.")
        else:
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**Select First Script:**")
                script1_options = [f"{s.get('type', 'Script')} - {s.get('test_case_id', 'Unknown')} ({s.get('timestamp', datetime.now()).strftime('%H:%M:%S')})"
                                 for s in filtered_scripts]
                script1_idx = st.selectbox(
                    "First script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script1"
                )

            with col2:
                st.markdown("**Select Second Script:**")
                script2_idx = st.selectbox(
                    "Second script",
                    options=range(len(script1_options)),
                    format_func=lambda x: script1_options[x],
                    key="compare_script2"
                )

            # Comparison type
            comparison_type = st.radio(
                "Comparison Type",
                options=["side_by_side", "unified"],
                format_func=lambda x: "Side by Side" if x == "side_by_side" else "Unified Diff",
                horizontal=True,
                key="comparison_type"
            )

            if st.button("🔄 Generate Comparison", use_container_width=True):
                if script1_idx != script2_idx:
                    script1 = filtered_scripts[script1_idx]
                    script2 = filtered_scripts[script2_idx]

                    comparison_html = generate_script_comparison(script1, script2, comparison_type)
                    st.components.v1.html(comparison_html, height=600, scrolling=True)
                else:
                    st.warning("Please select two different scripts for comparison.")

    # Independent Script Browser Footer
    st.markdown("---")
    st.markdown("### 📚 Script Browser Information")

    col1, col2 = st.columns(2)

    with col1:
        st.info("""
        **🔄 Always Accessible**: This Script Browser is available at any time, regardless of your current workflow stage.

        **📊 Persistent Storage**: All scripts are automatically saved and will be available in future sessions.
        """)

    with col2:
        st.info("""
        **🔍 Full History**: Browse scripts from all previous sessions and test cases.

        **⚡ Independent**: Use this tool without affecting your current workflow progress.
        """)

    # Optional workflow navigation (non-intrusive)
    with st.expander("🧭 Quick Workflow Navigation", expanded=False):
        st.markdown("*Optional: Jump to other workflow stages if needed*")

        col1, col2, col3 = st.columns(3)

        with col1:
            if st.button("📁 Upload CSV (Stage 1)", use_container_width=True):
                logger.info("User navigated to Stage 1 from Script Browser")
                state.advance_to(StateStage.STAGE1_UPLOAD, "User navigated to Stage 1 from Script Browser")
                st.rerun()

        with col2:
            if st.button("🎯 Test Cases (Stage 3)", use_container_width=True):
                logger.info("User navigated to Stage 3 from Script Browser")
                state.advance_to(StateStage.STAGE3_CONVERT, "User navigated to Stage 3 from Script Browser")
                st.rerun()

        with col3:
            if st.button("⚙️ Generate Scripts (Stage 6)", use_container_width=True):
                logger.info("User navigated to Stage 6 from Script Browser")
                state.advance_to(StateStage.STAGE6_GENERATE, "User navigated to Stage 6 from Script Browser")
                st.rerun()
